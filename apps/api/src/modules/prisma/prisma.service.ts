import { Injectable } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';
import { readReplicas } from '@prisma/extension-read-replicas';

export type PrismaTransactionalClient = Parameters<
  Parameters<PrismaClient['$transaction']>[0]
>[0];

function extendPrismaClient() {
  const DATABASE_URL = process.env.DATABASE_URL;
  if (!DATABASE_URL) {
    throw new Error('DATABASE_URL is not set');
  }
  let prisma = new PrismaClient({
    log: ['query'],
    datasources: {
      db: {
        url: DATABASE_URL,
      },
    },
    transactionOptions: {
      isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
      timeout: 10_000,
      maxWait: 10_000,
    },
  });

  const DATABASE_READ_REPLICAS_URLS =
    process.env.DATABASE_READ_REPLICAS_URLS ?? '';

  const readReplicasList =
    DATABASE_READ_REPLICAS_URLS.split(',').filter(Boolean);

  if (readReplicasList.length) {
    prisma = prisma.$extends(
      readReplicas({
        url: readReplicasList,
      }),
    ) as unknown as PrismaClient;
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  prisma.primary = function () {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return prisma.$primary ? (prisma.$primary() as PrismaClient) : this;
  };

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  prisma.readReplica = function () {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    return prisma.$replica ? (prisma.$replica() as PrismaClient) : this;
  };

  return prisma as PrismaClient & {
    primary: () => PrismaClient;
    readReplica: () => PrismaClient;
  };
}

const ExtendedPrismaClient = class {
  constructor() {
    return extendPrismaClient();
  }
} as new () => ReturnType<typeof extendPrismaClient>;

@Injectable()
export class PrismaService extends ExtendedPrismaClient {
  constructor() {
    super();
  }
}
