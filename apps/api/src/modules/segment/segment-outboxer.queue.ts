import { PrismaService } from '@modules/prisma/prisma.service';
import {
  JobMetadata,
  PgQueue,
  PgQueueJobFailedError,
} from '@modules/shared/queue/pg-queue.abstract';
import { Injectable } from '@nestjs/common';
// import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { Treatment } from '@prisma/client';

import { SegmentAdapter } from './segment.adapter';
import {
  SegmentIdentify,
  SegmentTrack,
  SegmentTrackEventName,
} from './segment.definitions';

export type TreatmentUpdatedPayload = Treatment;

type SegmentQueuePayload =
  | {
      type: 'track';
      event: SegmentTrackEventName;
      data: Omit<SegmentTrack, 'event' | 'userId'>;
    }
  | {
      type: 'identify';
      data: Omit<SegmentIdentify, 'userId'>;
    };

const WHITELIST_EVENTS: SegmentTrackEventName[] = [
  'LeadGenerated',
  'referralReceived',
  'WaitlistJoined',
] as const;

const WHITELIST_IDENTIFY_SOURCES = [
  'PatientReferFriendUseCase',
  'PatientLeadUseCase',
];

@Injectable()
export class SegmentOutboxerQueue extends PgQueue<SegmentQueuePayload> {
  constructor(
    private readonly segmentAdapter: SegmentAdapter,
    private readonly prisma: PrismaService,
  ) {
    super('segment.outboxer', {
      concurrency: 10,
      pollingIntervalSeconds: 0.5,
    });
  }

  protected async work(
    payload: SegmentQueuePayload,
    { patientId, source }: JobMetadata,
  ) {
    if (payload.type === 'track' && !WHITELIST_EVENTS.includes(payload.event)) {
      await this.assertPatientExistsInDatabase(patientId);
    }

    if (
      source &&
      payload.type === 'identify' &&
      !WHITELIST_IDENTIFY_SOURCES.includes(source)
    ) {
      await this.assertPatientExistsInDatabase(patientId);
    }

    if (payload.type === 'track') {
      return this.segmentAdapter.track({
        userId: patientId,
        event: payload.event,
        ...payload.data,
      });
    } else if (payload.type === 'identify') {
      return this.segmentAdapter.identify({
        userId: patientId,
        ...payload.data,
      });
    }
  }

  private async assertPatientExistsInDatabase(patientId: string) {
    const patient = await this.prisma.readReplica().patient.findUnique({
      where: { id: patientId },
    });

    if (!patient) {
      throw new PgQueueJobFailedError(
        'Patient not found for userId ' + patientId,
        {
          patientId,
        },
      );
    }
  }

  // @Cron(CronExpression.EVERY_HOUR)
  // async handleCron() {
  //   try {
  //     await this.prisma
  //       .$executeRaw`DELETE FROM "pgboss".job WHERE name = ${this.queueName} AND  "created_on" < NOW() - INTERVAL '5 hour' AND status = 'completed'`;
  //   } catch (error) {
  //     console.error('Error cleaning up completed jobs:', error.message);
  //   }
  // }
}
