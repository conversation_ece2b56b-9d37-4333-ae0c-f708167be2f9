import { runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { SegmentService } from '@/modules/segment/segment.service';
import { roles } from '@modules/auth/types/roles';
import { ChatService } from '@modules/chat/services/chat.service';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { DosespotPatient } from '@modules/dosespot/types/dosespot-patient';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { ORAL_SPECIFIC_NECESSITIES } from '@willow/utils';

@Injectable()
export class DoctorAcceptPatientUseCase {
  private readonly logger = new Logger(DoctorAcceptPatientUseCase.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly dosespotService: DosespotService,
    private readonly auditService: AuditService,
    private readonly onboardingStateService: OnboardingStateService,
    private readonly chatService: ChatService,
    private readonly segment: SegmentService,
  ) {}

  async execute(doctorUserId: string, patientId: string) {
    return runInDbTransaction(this.prismaService, async (prisma) => {
      const patient = await prisma.patient.findFirstOrThrow({
        where: { id: patientId, status: 'onboardingCompleted' },
        include: {
          user: true,
          pharmacy: true,
          shippingAddresses: {
            where: { default: true },
          },
          state: true,
        },
      });
      // check if patient has a doctor
      if (patient.doctorId) {
        throw new BadRequestException('Patient already has a doctor');
      }
      // Check if patient is already accepted
      if (patient.acceptedByUser) {
        throw new BadRequestException('Patient already accepted');
      }
      // validate doctor serving states with patient state
      const doctor = await this.prismaService.doctor.findFirstOrThrow({
        where: { userId: doctorUserId },
        include: {
          user: true,
          prescribesIn: { where: { stateId: patient.stateId } },
        },
      });

      // @todo check for max assignments per day, not relevant in MVP
      // Create this patient on dosespot
      const payload: DosespotPatient = {
        FirstName: patient.user.firstName,
        LastName: patient.user.lastName,
        DateOfBirth: patient.birthDate,
        Email: patient.user.email,
        Gender: patient.gender.toLocaleLowerCase() == 'male' ? 1 : 2,
        PrimaryPhoneType: 2,
        Active: true,
        PrimaryPhone: patient.user.phone.replace('+1', ''),
        Address1: patient.shippingAddresses[0].address1,
        Address2: patient.shippingAddresses[0].address2,
        City: patient.shippingAddresses[0].city,
        State: patient.state.code,
        ZipCode: patient.shippingAddresses[0].zip.padStart(5, '0'),
        Weight: patient.weight,
        Height: patient.height,
      };

      // create patient in DoseSpot and assign it to default pharmacy
      const dosespotPatient = await this.dosespotService.createPatient(
        payload,
        doctor.doseSpotClinicianId,
        patient.pharmacy.doseSpotPharmacyId,
      );

      // assign patient to doctor
      await prisma.doctorAssignment.create({
        data: {
          patientId: patientId,
          doctorId: doctor.id,
        },
      });
      // update patient with doctor id
      await prisma.patient.update({
        where: { id: patientId },
        data: {
          doctorId: doctor.id,
          doseSpotPatientId: String(dosespotPatient.Id),
          status: 'pendingApprovalFromDoctor',
          acceptedByUser: doctorUserId,
          acceptedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      const conversationId =
        await this.chatService.createPatientDoctorConversation({
          patientUserId: patient.userId,
          doctorUserId: doctorUserId,
          prisma,
        });

      const version = patient.onboardingVersion as OnboardingVersion;
      if (
        this.onboardingStateService.hasCapability(
          version,
          'hasBMICalculation',
        ) &&
        conversationId
      ) {
        await this.checkMedicalNecessityMismatch(
          patientId,
          doctorUserId,
          conversationId,
          prisma,
        );
      }

      void this.auditService.append({
        patientId: patient.id,
        action: 'PATIENT_ACCEPTED',
        actorType: 'DOCTOR',
        actorId: doctor.id,
        resourceType: 'PATIENT',
        resourceId: patient.id,
        details: {
          doctorId: doctor.id,
          doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
          doseSpotPatientId: dosespotPatient.Id,
        },
      });

      await this.segment.identify(
        patient.id,
        {
          traits: {
            doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
            doctorID: doctor.id,
            defaultPharmacy: patient.pharmacy.name,
          },
        },
        { prisma, source: 'DoctorAcceptPatientUseCase' },
      );
      await this.segment.track(
        patient.id,
        'PatientAccepted',
        {
          properties: {
            doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
            doctorID: doctor.id,
            patientName: `${patient.user.firstName} ${patient.user.lastName}`,
            patientID: patient.id,
          },
        },
        { prisma },
      );
    });
  }

  /**
   * Checks for medical necessity mismatch and sends doctor note if conditions are met
   */
  private async checkMedicalNecessityMismatch(
    patientId: string,
    doctorUserId: string,
    conversationId: string,
    prisma: Prisma.TransactionClient,
  ): Promise<void> {
    try {
      // Get patient's medical necessities
      const medicalNecessities = await prisma.medicalNecessity.findMany({
        where: { patientId },
      });

      // Get patient's desired treatments with product details
      const desiredTreatments = await prisma.patientDesiredTreatment.findMany({
        where: { patientId },
        include: {
          product: {
            select: { form: true, name: true },
          },
        },
      });

      // Filter out the auto-added bmiUnder27 to check what patient actually selected
      const patientSelectedNecessities = medicalNecessities.filter(
        (n) => n.necessity !== 'bmiUnder27',
      ) as { necessity: (typeof ORAL_SPECIFIC_NECESSITIES)[number] }[];

      // Check conditions for mismatch
      // 1. Patient selected ONLY oral-specific necessities (and at least one)
      const hasOnlyOralNecessities =
        patientSelectedNecessities.length > 0 &&
        patientSelectedNecessities.every((n) =>
          ORAL_SPECIFIC_NECESSITIES.includes(n.necessity),
        );

      // 2. BMI is 27 or above (no bmiUnder27 necessity was added)
      const bmiIs27OrAbove = !medicalNecessities.some(
        (n) => n.necessity === 'bmiUnder27',
      );

      // 3. Patient selected injectable product
      const hasInjectableProduct = desiredTreatments.some(
        (dt) => dt.product.form === 'injectable',
      );

      // Send doctor note if all conditions are met
      if (hasOnlyOralNecessities && bmiIs27OrAbove && hasInjectableProduct) {
        const messageContent =
          'Please reach out to patient regarding mismatch in medical necessity and prescription. Patient has medical necessity reasoning for oral products but has chosen to prefer injectables.';

        await this.chatService.sendMessage(
          {
            conversationId,
            userId: doctorUserId,
            content: messageContent,
            contentType: 'text',
            type: 'doctorNote',
            role: roles.Doctor,
            needsReply: false,
          },
          { prisma },
        );
      }
    } catch (error) {
      // Log the error but don't throw it to avoid disrupting the patient acceptance process
      this.logger.error(
        `Error checking medical necessity mismatch: ${error.message}`,
        error.stack,
      );
    }
  }
}
