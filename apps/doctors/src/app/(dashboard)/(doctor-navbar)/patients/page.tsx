'use client';

import type { ListedPatient } from '@/data/types';
import type { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetPatients } from '@/hooks/patients';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { parseAsString, useQueryState } from 'nuqs';

import { cn } from '@willow/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';
import { ColumnHeader } from '@willow/ui/column-header';
import { DateDistance } from '@willow/ui/date-distance';
import { Pagination } from '@willow/ui/pagination';
import { SearchInput } from '@willow/ui/search-input';
import {
  usePaginationSearchParams,
  useSortingQueryState,
} from '@willow/utils/table';

import send from '~/assets/svg/send.svg';
import { env } from '~/env';

export default function PatientsListPage() {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [query, setQuery] = useQueryState(
    'query',
    parseAsString.withDefault(''),
  );
  const [pagination, setPagination] = usePaginationSearchParams();
  const [sorting, setSorting] = useSortingQueryState([
    {
      id: 'lastMessageAt',
      desc: true,
    },
  ]);

  const fetchParams = useMemo(
    () => ({
      sortBy: sorting.length > 0 ? sorting[0]?.id : 'lastMessageAt',
      direction:
        sorting.length > 0 ? (sorting[0]?.desc ? 'desc' : 'asc') : 'asc',
      page: pagination.page,
      limit: pagination.pageSize,
      search: query ?? undefined,
    }),
    [query, pagination, sorting],
  );
  const timestamp = new Date().getTime();

  const { data, isPending } = useGetPatients(fetchParams);

  const columns: ColumnDef<ListedPatient>[] = useMemo(
    () => [
      {
        accessorKey: 'user.firstName',
        id: 'name',
        header: () => <ColumnHeader label="Name" sortKey="name" />,
        cell: ({ row }) => (
          <div className="flex items-center gap-3">
            <div
              style={{ width: '40px', height: '40px', position: 'relative' }}
            >
              <Avatar>
                <AvatarImage
                  src={`${env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL}/${row.original.facePhoto}?${timestamp}`}
                />
                <AvatarFallback className="font-bold uppercase text-denim-light">
                  {row.original.user.firstName?.[0]}
                  {row.original.user.lastName?.[0]}
                </AvatarFallback>
              </Avatar>
            </div>
            <Link
              href={`/patients/${row.original.id}`}
              className="text-sm text-[#FE6232] hover:underline"
            >
              {row.original.user.firstName} {row.original.user.lastName}
            </Link>
          </div>
        ),
      },
      {
        accessorKey: 'conversation.updatedAt',
        id: 'lastMessageAt',
        header: () => (
          <ColumnHeader label="Last message" sortKey="lastMessageAt" />
        ),
        cell: ({ row }) =>
          row.original.conversation.updatedAt ? (
            <DateDistance
              date={new Date(row.original.conversation.updatedAt)}
              cutoffUnit="hour"
              cutoffValue={2}
            />
          ) : (
            'N/A'
          ),
      },
      {
        accessorKey: 'conversation.watcher.0.unreadMessages',
        header: 'New messages',
        cell: ({ row }) => (
          <div className="text-sm">
            {row.original.conversation.watcher?.[0]?.unreadMessages ?? 0}
          </div>
        ),
      },
      {
        accessorKey: 'coveredFrom',
        header: 'Covered From',
        cell: () => <div className="text-sm">N/A</div>,
      },
      {
        id: 'actions',
        cell: ({ row }) => (
          <div className="flex items-center gap-3 text-sm text-[#FE6232]">
            <Link
              href={`/patients/${row.original.id}/messages`}
              className="flex gap-2"
            >
              <Image
                src={send}
                width={20}
                height={20}
                className="rounded-full"
                alt="message icon"
              />
              <span>Message</span>
            </Link>
          </div>
        ),
      },
    ],
    [],
  );

  const table = useReactTable({
    data: data?.patients || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: (sorting) => {
      void setSorting(sorting);
    },
    state: {
      sorting,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
    manualPagination: true,
    pageCount: data?.pagination.totalPages || -1,
  });

  const handleSearchSubmit = useCallback(
    (query: string | null) => {
      void setQuery(query);
      void setPagination({ page: 1 });
    },
    [setPagination, setQuery],
  );

  return (
    <div className="flex h-full w-full flex-col px-10">
      <div className="grid h-[calc(100vh-96px)] grid-rows-[auto_1fr_auto]">
        <div className="">
          <div className="text-2xl font-medium text-denim">Patients</div>
          <SearchInput
            classaName="mb-5"
            ref={searchInputRef}
            onSearch={handleSearchSubmit}
            placeholder="Search patients"
            defaultValue={query}
          />

          {data?.patients && data.patients.length > 0 && !isPending && (
            <div className="mb-4">
              <span className="text-sm font-normal text-slate-600">All</span>
              <span className="ml-1 rounded-lg bg-slate-600 px-2 py-1 text-xs text-white">
                {data.pagination.totalCount ?? 0}
              </span>
            </div>
          )}
        </div>

        <div className="overflow-y-scroll">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="p-2">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {isPending ? 'Loading...' : 'No results.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <div className={cn('flex items-center justify-end py-4')}>
          {table.getRowModel().rows.length > 0 && (
            <Pagination
              currentPage={pagination.page}
              totalPages={table.getPageCount()}
              onPageChange={(page) => setPagination({ page })}
            />
          )}
        </div>
      </div>
    </div>
  );
}
