'use client';

import { useEffect, useState } from 'react';

import { Notification } from '@willow/ui/base/notification';

import { useGetDashboardHomePageData } from '~/hooks/dashboard';
import { FollowUpsColumn } from './_components/follow-ups-column';
import { IntakeColumn } from './_components/intake-column';
import { MessagesColumn } from './_components/messages-column';
import { PrescribeColumn } from './_components/prescribe-column';

const Home = () => {
  const [transferred, setTransferred] = useState(0);
  const { data } = useGetDashboardHomePageData();
  useEffect(() => {
    if (data) {
      const transferred = data.pendingApprovalPatients.filter(
        (p) =>
          p.historicAssignments.length > 0 &&
          !p.historicAssignments[0]?.BulkTransfer,
      ).length;
      setTransferred(transferred);
    }
  }, [data]);
  return (
    <div>
      {transferred > 0 && (
        <Notification variant="warning" closable>
          You have {transferred} transferred patient
          {transferred > 1 ? 's ' : ' '}
          ready to be prescribed
        </Notification>
      )}
      <div className="mt-4 flex h-full w-full flex-col justify-between gap-10 px-10 pb-20 md:flex-row">
        <IntakeColumn />
        <MessagesColumn />
        <PrescribeColumn />
        <FollowUpsColumn />
      </div>
    </div>
  );
};

export default Home;
