'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useGetDashboardHomePageData } from '@/hooks/dashboard';

import { cn } from '@willow/ui';
import { Button, buttonVariants } from '@willow/ui/base/button';
import { useToast } from '@willow/ui/base/use-toast';

import type { AvailablePatient } from '~/data/dashboard-types';
import { useAcceptPatient } from '~/hooks/patient';
import { PatientsColumn } from '../_components/patients-column';
import { PatientCard } from './patient-card';

export const IntakeColumn = () => {
  const { data, isPending } = useGetDashboardHomePageData();

  return (
    <PatientsColumn
      count={
        (data?.availablePatients?.length ?? 0) +
        (data?.pendingApprovalPatients.length ?? 0) +
        (data?.revalidatedPatients.length ?? 0)
      }
      title="Intake"
      isLoading={isPending}
    >
      {data?.revalidatedPatients.map((p) => {
        return (
          <PatientCard
            key={p.id}
            header={{ label: 'Pending Action', className: 'bg-gray-500' }}
            patient={{
              firstName: p.user.firstName,
              lastName: p.user.lastName,
              stateCode: p.state.code,
              distanceDate: p.acceptedAt,
              displayDate: p.birthDate,
            }}
            Footer={
              <>
                <Link
                  href={`/patients/${p.id}/messages`}
                  className={cn(
                    buttonVariants({ variant: 'tertiary', size: 'sm' }),
                    'w-full rounded-none',
                  )}
                >
                  Message
                </Link>
                <Link
                  href={`/patients/${p.id}`}
                  className={cn(
                    buttonVariants({ variant: 'denim', size: 'sm' }),
                    'w-full rounded-none text-white',
                  )}
                >
                  View Profile
                </Link>
              </>
            }
          />
        );
      })}
      {data?.availablePatients.map((p) => (
        <AvailablePatientCard key={p.id} patient={p} />
      ))}
      {data?.pendingApprovalPatients.map((p) => {
        const transferredFrom =
          p.historicAssignments[0]?.previousAssignment.doctor.user.lastName;
        const isBulkTransfer = !!p.historicAssignments[0]?.BulkTransfer;
        return (
          <PatientCard
            key={p.id}
            header={{
              label:
                transferredFrom && !isBulkTransfer
                  ? `Transferred from Dr. ${transferredFrom}`
                  : 'Pending Action',
              className:
                transferredFrom && !isBulkTransfer ? 'bg-denim' : 'bg-gray-500',
            }}
            patient={{
              firstName: p.user.firstName,
              lastName: p.user.lastName,
              stateCode: p.state.code,
              distanceDate: p.acceptedAt,
              displayDate: p.birthDate,
            }}
            Footer={
              <>
                <Link
                  href={`/patients/${p.id}/messages`}
                  className={cn(
                    buttonVariants({ variant: 'tertiary', size: 'sm' }),
                    'w-full rounded-none',
                  )}
                >
                  Message
                </Link>
                <Link
                  href={`/patients/${p.id}`}
                  className={cn(
                    buttonVariants({ variant: 'denim', size: 'sm' }),
                    'w-full rounded-none text-white',
                  )}
                >
                  View Profile
                </Link>
              </>
            }
          />
        );
      })}
    </PatientsColumn>
  );
};

const AvailablePatientCard = ({ patient }: { patient: AvailablePatient }) => {
  const { toast } = useToast();
  const router = useRouter();

  const { mutate: acceptPatient, isPending } = useAcceptPatient({
    onSuccess: () => {
      router.push(`/patients/${patient.id}`);
    },
    onError: () => {
      toast({ title: 'Failed to accept patient', variant: 'destructive' });
    },
  });
  return (
    <PatientCard
      patient={{
        firstName: patient.user.firstName,
        lastName: patient.user.lastName,
        stateCode: patient.state.code,
        distanceDate: patient.completedAt,
        displayDate: patient.birthDate,
      }}
      Footer={
        <Button
          className="w-full rounded-none text-white"
          variant={'denim'}
          size={'sm'}
          loading={isPending}
          onClick={() => acceptPatient(patient.id)}
        >
          Accept
        </Button>
      }
    />
  );
};
